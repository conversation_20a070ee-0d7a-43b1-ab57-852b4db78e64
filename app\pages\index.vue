<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <UContainer class="py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
          Math Game Center
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Master mathematical notation and function analysis through interactive games.
          Choose your challenge and start learning!
        </p>
      </div>

      <!-- Game Mode Selection -->
      <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <!-- Notation Conversion Game -->
        <UCard
          class="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-8 ring-1 ring-gray-200 dark:ring-gray-700 shadow-lg"
        >
          <template #header>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Icon name="i-lucide-graduation-cap" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                Notation Conversion
              </h2>
            </div>
          </template>

          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              Convert between algebraic statements, interval notation, and set-builder notation.
              Master the different ways to express mathematical relationships.
            </p>

            <div class="space-y-2">
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Algebraic ↔ Interval notation</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Set-builder ↔ Interval notation</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>37 challenging problems</span>
              </div>
            </div>

            <UButton
              @click="startGame('notation-conversion')"
              size="lg"
              class="w-full"
              :loading="loading"
            >
              Start Notation Game
            </UButton>
          </div>
        </UCard>

        <!-- Domain and Range Game -->
        <UCard
          class="hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-8 ring-1 ring-gray-200 dark:ring-gray-700 shadow-lg"
        >
          <template #header>
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <Icon name="i-lucide-bar-chart-3" class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                Domain & Range
              </h2>
            </div>
          </template>

          <div class="space-y-4">
            <p class="text-gray-600 dark:text-gray-300">
              Analyze functions and determine their domains and ranges.
              Work with linear, quadratic, cubic, absolute value, and square root functions.
            </p>

            <div class="space-y-2">
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Multiple function types</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>Domain and range analysis</span>
              </div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Icon name="i-lucide-check-circle" class="w-4 h-4 text-green-500" />
                <span>37 progressive challenges</span>
              </div>
            </div>

            <UButton
              @click="startGame('domain-range')"
              size="lg"
              color="success"
              class="w-full"
              :loading="loading"
            >
              Start Domain & Range Game
            </UButton>
          </div>
        </UCard>
      </div>

      <!-- Features Section -->
      <div class="mt-16 text-center">
        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">
          Game Features
        </h3>
        <div class="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto">
          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-puzzle" class="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Interactive Learning</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Hands-on practice with immediate feedback and hints
            </p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-pie-chart" class="w-8 h-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Progress Tracking</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Visual progress indicators and completion statistics
            </p>
          </div>
          <div class="text-center">
            <div class="w-16 h-16 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="i-lucide-tablet" class="w-8 h-8 text-pink-600 dark:text-pink-400" />
            </div>
            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Mobile Friendly</h4>
            <p class="text-sm text-gray-600 dark:text-gray-300">
              Optimized for classroom use on tablets and laptops
            </p>
          </div>
        </div>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import type { GameMode } from '~/types/game'

// SEO Meta
useSeoMeta({
  title: 'Math Game Center - Interactive Mathematical Learning',
  description: 'Master mathematical notation and function analysis through interactive games. Practice notation conversion and domain & range problems.',
  ogTitle: 'Math Game Center - Interactive Mathematical Learning',
  ogDescription: 'Master mathematical notation and function analysis through interactive games.',
})

// State
const loading = ref(false)

// Actions
const startGame = async (mode: GameMode) => {
  loading.value = true

  try {
    // Navigate to the appropriate game page
    await navigateTo(`/${mode}`)
  } catch (error) {
    console.error('Navigation error:', error)
  } finally {
    loading.value = false
  }
}
</script>