{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true}}, "overrides": [{"includes": ["**/*.vue"], "linter": {"rules": {"correctness": {"noUnusedVariables": "off"}, "style": {"useImportType": "off"}}}}, {"includes": ["**/*.ts", "**/*.tsx"], "linter": {"rules": {"style": {"useImportType": "warn"}}}}], "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}