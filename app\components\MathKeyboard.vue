<template>
  <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 space-y-4">
    <div class="space-y-2" v-for="section in keyboardSections" :key="section.title">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ section.title }}</h4>
      <div class="grid gap-2" :class="section.gridClass">
        <UButton
          v-for="key in section.keys"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="sm"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-base min-w-[2.5rem] h-10 sm:text-sm sm:min-w-[2rem] sm:h-8"
        >
          {{ key.symbol }}
        </UButton>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
      <UButton @click="clearInput" variant="outline" size="sm" icon="i-lucide-eraser">
        Clear
      </UButton>
      <UButton @click="backspace" variant="outline" size="sm" icon="i-lucide-delete">
        Backspace
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MathKeyboardKey } from '~/types/game'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Keyboard layout organized by sections
const keyboardSections = computed(() => [
  {
    title: 'Numbers',
    gridClass: 'numbers-grid',
    keys: [
      { symbol: '1', label: 'One', category: 'number' },
      { symbol: '2', label: 'Two', category: 'number' },
      { symbol: '3', label: 'Three', category: 'number' },
      { symbol: '4', label: 'Four', category: 'number' },
      { symbol: '5', label: 'Five', category: 'number' },
      { symbol: '6', label: 'Six', category: 'number' },
      { symbol: '7', label: 'Seven', category: 'number' },
      { symbol: '8', label: 'Eight', category: 'number' },
      { symbol: '9', label: 'Nine', category: 'number' },
      { symbol: '0', label: 'Zero', category: 'number' },
      { symbol: '-', label: 'Negative', category: 'number' },
      { symbol: '.', label: 'Decimal', category: 'number' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Variables & Operators',
    gridClass: 'operators-grid',
    keys: [
      { symbol: 'x', label: 'Variable x', category: 'operator' },
      { symbol: 'y', label: 'Variable y', category: 'operator' },
      { symbol: '+', label: 'Plus', category: 'operator' },
      { symbol: '=', label: 'Equals', category: 'operator' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Inequalities',
    gridClass: 'inequalities-grid',
    keys: [
      { symbol: '<', label: 'Less than', category: 'inequality' },
      { symbol: '>', label: 'Greater than', category: 'inequality' },
      { symbol: '≤', label: 'Less than or equal', category: 'inequality' },
      { symbol: '≥', label: 'Greater than or equal', category: 'inequality' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Interval Notation',
    gridClass: 'interval-grid',
    keys: [
      { symbol: '(', label: 'Open parenthesis', category: 'interval' },
      { symbol: ')', label: 'Close parenthesis', category: 'interval' },
      { symbol: '[', label: 'Open bracket', category: 'interval' },
      { symbol: ']', label: 'Close bracket', category: 'interval' },
      { symbol: ',', label: 'Comma', category: 'interval' },
      { symbol: '∞', label: 'Infinity', category: 'interval' },
      { symbol: '∪', label: 'Union', category: 'interval' },
      { symbol: '∩', label: 'Intersection', category: 'interval' }
    ] as MathKeyboardKey[]
  },
  {
    title: 'Set Notation',
    gridClass: 'set-grid',
    keys: [
      { symbol: '{', label: 'Open brace', category: 'set' },
      { symbol: '}', label: 'Close brace', category: 'set' },
      { symbol: '|', label: 'Such that', category: 'set' }
    ] as MathKeyboardKey[]
  }
])

const getCategoryColor = (category: string): 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'neutral' => {
  const colorMap: Record<string, 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'neutral'> = {
    'number': 'primary',
    'operator': 'secondary',
    'inequality': 'error',
    'interval': 'success',
    'set': 'warning'
  }
  return colorMap[category] || 'neutral'
}

const insertSymbol = (symbol: string) => {
  const newValue = props.modelValue + symbol
  emit('update:modelValue', newValue)
}

const clearInput = () => {
  emit('update:modelValue', '')
}

const backspace = () => {
  const newValue = props.modelValue.slice(0, -1)
  emit('update:modelValue', newValue)
}
</script>

