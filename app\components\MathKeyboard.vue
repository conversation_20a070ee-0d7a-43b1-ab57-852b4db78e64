<template>
  <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
    <!-- QWERTY Keyboard Layout -->
    <div class="space-y-2">
      <!-- Row 1: Numbers and symbols -->
      <div class="flex gap-1 justify-center">
        <UButton
          v-for="key in numberRow"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="lg"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-lg min-w-[3rem] h-12 flex-1 max-w-[3.5rem]"
        >
          {{ key.symbol }}
        </UButton>
      </div>

      <!-- Row 2: First letter row with math symbols -->
      <div class="flex gap-1 justify-center">
        <UButton
          v-for="key in firstRow"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="lg"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-lg min-w-[3rem] h-12 flex-1 max-w-[3.5rem]"
        >
          {{ key.symbol }}
        </UButton>
      </div>

      <!-- Row 3: Second letter row with more math symbols -->
      <div class="flex gap-1 justify-center">
        <UButton
          v-for="key in secondRow"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="lg"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-lg min-w-[3rem] h-12 flex-1 max-w-[3.5rem]"
        >
          {{ key.symbol }}
        </UButton>
      </div>

      <!-- Row 4: Third letter row with brackets and special symbols -->
      <div class="flex gap-1 justify-center">
        <UButton
          v-for="key in thirdRow"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="lg"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-lg min-w-[3rem] h-12 flex-1 max-w-[3.5rem]"
        >
          {{ key.symbol }}
        </UButton>
      </div>

      <!-- Row 5: Brackets and set notation -->
      <div class="flex gap-1 justify-center">
        <UButton
          v-for="key in bracketRow"
          :key="key.symbol"
          @click="insertSymbol(key.symbol)"
          variant="outline"
          size="lg"
          :color="getCategoryColor(key.category)"
          :title="key.label"
          class="font-mono text-lg min-w-[3rem] h-12 flex-1 max-w-[3.5rem]"
        >
          {{ key.symbol }}
        </UButton>
      </div>

      <!-- Row 6: Space bar and action buttons -->
      <div class="flex gap-2 justify-center items-center mt-3">
        <UButton @click="clearInput" variant="outline" size="lg" icon="i-lucide-eraser" class="px-4">
          Clear
        </UButton>
        <UButton @click="backspace" variant="outline" size="lg" icon="i-lucide-delete" class="px-4">
          ⌫
        </UButton>
        <UButton @click="insertSymbol(' ')" variant="outline" size="lg" class="flex-1 max-w-[200px]">
          Space
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MathKeyboardKey } from '~/types/game'

interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// QWERTY Keyboard layout organized by rows
const numberRow = computed(() => [
  { symbol: '1', label: 'One', category: 'number' },
  { symbol: '2', label: 'Two', category: 'number' },
  { symbol: '3', label: 'Three', category: 'number' },
  { symbol: '4', label: 'Four', category: 'number' },
  { symbol: '5', label: 'Five', category: 'number' },
  { symbol: '6', label: 'Six', category: 'number' },
  { symbol: '7', label: 'Seven', category: 'number' },
  { symbol: '8', label: 'Eight', category: 'number' },
  { symbol: '9', label: 'Nine', category: 'number' },
  { symbol: '0', label: 'Zero', category: 'number' },
  { symbol: '-', label: 'Negative', category: 'number' },
  { symbol: '=', label: 'Equals', category: 'operator' }
] as MathKeyboardKey[])

const firstRow = computed(() => [
  { symbol: 'q', label: 'Q', category: 'operator' },
  { symbol: 'w', label: 'W', category: 'operator' },
  { symbol: 'e', label: 'E', category: 'operator' },
  { symbol: 'r', label: 'R', category: 'operator' },
  { symbol: 't', label: 'T', category: 'operator' },
  { symbol: 'y', label: 'Variable y', category: 'operator' },
  { symbol: 'u', label: 'U', category: 'operator' },
  { symbol: 'i', label: 'I', category: 'operator' },
  { symbol: 'o', label: 'O', category: 'operator' },
  { symbol: 'p', label: 'P', category: 'operator' },
  { symbol: '+', label: 'Plus', category: 'operator' },
  { symbol: '∞', label: 'Infinity', category: 'interval' }
] as MathKeyboardKey[])

const secondRow = computed(() => [
  { symbol: 'a', label: 'A', category: 'operator' },
  { symbol: 's', label: 'S', category: 'operator' },
  { symbol: 'd', label: 'D', category: 'operator' },
  { symbol: 'f', label: 'F', category: 'operator' },
  { symbol: 'g', label: 'G', category: 'operator' },
  { symbol: 'h', label: 'H', category: 'operator' },
  { symbol: 'j', label: 'J', category: 'operator' },
  { symbol: 'k', label: 'K', category: 'operator' },
  { symbol: 'l', label: 'L', category: 'operator' },
  { symbol: '<', label: 'Less than', category: 'inequality' },
  { symbol: '>', label: 'Greater than', category: 'inequality' },
  { symbol: '≤', label: 'Less than or equal', category: 'inequality' }
] as MathKeyboardKey[])

const thirdRow = computed(() => [
  { symbol: 'z', label: 'Z', category: 'operator' },
  { symbol: 'x', label: 'Variable x', category: 'operator' },
  { symbol: 'c', label: 'C', category: 'operator' },
  { symbol: 'v', label: 'V', category: 'operator' },
  { symbol: 'b', label: 'B', category: 'operator' },
  { symbol: 'n', label: 'N', category: 'operator' },
  { symbol: 'm', label: 'M', category: 'operator' },
  { symbol: '.', label: 'Decimal', category: 'number' },
  { symbol: ',', label: 'Comma', category: 'interval' },
  { symbol: '≥', label: 'Greater than or equal', category: 'inequality' },
  { symbol: '∪', label: 'Union', category: 'interval' },
  { symbol: '∩', label: 'Intersection', category: 'interval' }
] as MathKeyboardKey[])

const bracketRow = computed(() => [
  { symbol: '(', label: 'Open parenthesis', category: 'interval' },
  { symbol: ')', label: 'Close parenthesis', category: 'interval' },
  { symbol: '[', label: 'Open bracket', category: 'interval' },
  { symbol: ']', label: 'Close bracket', category: 'interval' },
  { symbol: '{', label: 'Open brace', category: 'set' },
  { symbol: '}', label: 'Close brace', category: 'set' },
  { symbol: '|', label: 'Such that', category: 'set' }
] as MathKeyboardKey[])

const getCategoryColor = (category: string): 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'neutral' => {
  const colorMap: Record<string, 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'neutral'> = {
    'number': 'primary',
    'operator': 'secondary',
    'inequality': 'error',
    'interval': 'success',
    'set': 'warning'
  }
  return colorMap[category] || 'neutral'
}

const insertSymbol = (symbol: string) => {
  const newValue = props.modelValue + symbol
  emit('update:modelValue', newValue)
}

const clearInput = () => {
  emit('update:modelValue', '')
}

const backspace = () => {
  const newValue = props.modelValue.slice(0, -1)
  emit('update:modelValue', newValue)
}
</script>

