import type { ValidationResult, NotationProblem, DomainRangeProblem } from '~/types/game'

/**
 * Normalize mathematical expressions for comparison
 */
export function normalizeExpression(expr: string): string {
  return expr
    .replace(/\s+/g, '') // Remove all whitespace
    .replace(/\(/g, '(')
    .replace(/\)/g, ')')
    .replace(/∞/g, 'infinity')
    .replace(/∪/g, 'union')
    .replace(/∩/g, 'intersection')
    .replace(/≤/g, '<=')
    .replace(/≥/g, '>=')
    .replace(/\{/g, '{')
    .replace(/\}/g, '}')
    .replace(/\|/g, '|')
    .toLowerCase()
}

/**
 * Validate interval notation answers
 */
export function validateInterval(userAnswer: string, correctAnswers: string[]): ValidationResult {
  const normalized = normalizeExpression(userAnswer)

  for (const correct of correctAnswers) {
    const normalizedCorrect = normalizeExpression(correct)
    if (normalized === normalizedCorrect) {
      return {
        isCorrect: true,
        message: 'Correct! Well done.',
        acceptedAnswer: correct
      }
    }
  }

  // Check for common mistakes and provide helpful feedback
  if (normalized.includes('(') && normalized.includes(')')) {
    if (!normalized.includes(',')) {
      return {
        isCorrect: false,
        message: 'Remember to separate the bounds with a comma in interval notation.'
      }
    }
  }

  if (normalized.includes('{') && normalized.includes('}')) {
    return {
      isCorrect: false,
      message: 'This looks like set-builder notation. Try using interval notation with parentheses and brackets.'
    }
  }

  return {
    isCorrect: false,
    message: 'Not quite right. Check your interval notation syntax.'
  }
}

/**
 * Validate set-builder notation answers
 */
export function validateSetBuilder(userAnswer: string, correctAnswers: string[]): ValidationResult {
  const normalized = normalizeExpression(userAnswer)

  for (const correct of correctAnswers) {
    const normalizedCorrect = normalizeExpression(correct)
    if (normalized === normalizedCorrect) {
      return {
        isCorrect: true,
        message: 'Excellent! Your set-builder notation is correct.',
        acceptedAnswer: correct
      }
    }
  }

  if (!normalized.includes('{') || !normalized.includes('}')) {
    return {
      isCorrect: false,
      message: 'Set-builder notation should be enclosed in curly braces { }.'
    }
  }

  if (!normalized.includes('|')) {
    return {
      isCorrect: false,
      message: 'Remember to use the vertical bar | to separate the variable from the condition.'
    }
  }

  return {
    isCorrect: false,
    message: 'Check your set-builder notation. Make sure you have the correct variable and condition.'
  }
}

/**
 * Validate algebraic inequality answers
 */
export function validateAlgebraic(userAnswer: string, correctAnswers: string[]): ValidationResult {
  const normalized = normalizeExpression(userAnswer)

  for (const correct of correctAnswers) {
    const normalizedCorrect = normalizeExpression(correct)
    if (normalized === normalizedCorrect) {
      return {
        isCorrect: true,
        message: 'Perfect! Your algebraic expression is correct.',
        acceptedAnswer: correct
      }
    }
  }

  return {
    isCorrect: false,
    message: 'Check your inequality. Make sure you have the correct variable, operator, and value.'
  }
}

/**
 * Main validation function that routes to appropriate validator
 */
export function validateAnswer(
  userAnswer: string,
  problem: NotationProblem | DomainRangeProblem
): ValidationResult {
  if (!userAnswer.trim()) {
    return {
      isCorrect: false,
      message: 'Please enter an answer.'
    }
  }

  if (problem.type === 'notation-conversion') {
    const notationProblem = problem as NotationProblem

    switch (notationProblem.targetNotation) {
      case 'interval':
        return validateInterval(userAnswer, notationProblem.correctAnswers)
      case 'set-builder':
        return validateSetBuilder(userAnswer, notationProblem.correctAnswers)
      case 'algebraic':
        return validateAlgebraic(userAnswer, notationProblem.correctAnswers)
      default:
        return {
          isCorrect: false,
          message: 'Unknown notation type.'
        }
    }
  } else {
    // Domain and Range validation
    const drProblem = problem as DomainRangeProblem
    const correctAnswers = drProblem.questionType === 'domain'
      ? drProblem.correctDomain || []
      : drProblem.correctRange || []

    return validateInterval(userAnswer, correctAnswers)
  }
}