<template>
  <div class="game-grid">
    <div
      v-for="itemId in 37"
      :key="itemId"
      class="game-item"
      :class="getItemClasses(itemId)"
      @click="handleItemClick(itemId)"
      :aria-label="`Problem ${itemId}${gameStore.state.items[itemId]?.completed ? ' - Completed' : ''}`"
      role="button"
      tabindex="0"
      @keydown.enter="handleItemClick(itemId)"
      @keydown.space.prevent="handleItemClick(itemId)"
    >
      <div class="item-number">{{ itemId }}</div>
      <div v-if="gameStore.state.items[itemId]?.completed" class="completion-indicator">
        <Icon name="i-heroicons-check" class="w-5 h-5" />
      </div>
      <div v-if="gameStore.state.items[itemId]?.attempts > 0 && !gameStore.state.items[itemId]?.completed"
           class="attempt-indicator">
        {{ gameStore.state.items[itemId].attempts }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  // No props needed as we're using the global store
}

interface Emits {
  (e: 'item-selected', itemId: number): void
}

const emit = defineEmits<Emits>()
const gameStore = useGameStore()

const handleItemClick = (itemId: number) => {
  if (gameStore.state.items[itemId]?.completed) {
    return // Don't allow clicking completed items
  }

  emit('item-selected', itemId)
}

const getItemClasses = (itemId: number) => {
  const item = gameStore.state.items[itemId]

  return {
    'completed': item?.completed,
    'attempted': item?.attempts > 0 && !item?.completed,
    'available': !item?.completed
  }
}
</script>

<style scoped>
.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.game-item {
  @apply relative aspect-square bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-lg shadow-sm transition-all duration-200 cursor-pointer;
  @apply flex flex-col items-center justify-center text-lg font-semibold;
  @apply hover:shadow-md hover:border-blue-300 dark:hover:border-blue-600;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.game-item.available {
  @apply hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:scale-105;
}

.game-item.completed {
  @apply bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 cursor-default;
  @apply text-green-700 dark:text-green-300;
}

.game-item.completed:hover {
  @apply scale-100 shadow-sm;
}

.game-item.attempted {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600;
  @apply text-yellow-700 dark:text-yellow-300;
}

.item-number {
  @apply text-xl font-bold;
}

.completion-indicator {
  @apply absolute top-1 right-1 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center;
}

.attempt-indicator {
  @apply absolute top-1 right-1 w-5 h-5 bg-yellow-500 text-white text-xs rounded-full flex items-center justify-center;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .game-grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.75rem;
  }

  .item-number {
    @apply text-base;
  }
}

@media (min-width: 1024px) {
  .game-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}
</style>