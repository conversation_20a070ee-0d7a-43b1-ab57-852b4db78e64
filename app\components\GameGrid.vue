<template>
  <div class="grid grid-cols-[repeat(auto-fit,minmax(80px,1fr))] gap-4 max-w-3xl mx-auto sm:grid-cols-[repeat(auto-fit,minmax(60px,1fr))] sm:gap-3 lg:grid-cols-[repeat(auto-fit,minmax(100px,1fr))]">
    <UButton
      v-for="itemId in 37"
      :key="itemId"
      @click="handleItemClick(itemId)"
      :disabled="gameStore.state.items[itemId]?.completed"
      :variant="getItemVariant(itemId)"
      :color="getItemColor(itemId)"
      size="lg"
      class="aspect-square min-h-[80px] transition-all duration-200 hover:scale-105 disabled:cursor-default disabled:hover:scale-100"
      :class="getItemClasses(itemId)"
      :aria-label="`Problem ${itemId}${gameStore.state.items[itemId]?.completed ? ' - Completed' : ''}`"
    >
      <div class="relative w-full h-full flex items-center justify-center">
        <span class="text-xl font-bold sm:text-base">{{ itemId }}</span>
        <Icon
          v-if="gameStore.state.items[itemId]?.completed"
          name="i-lucide-check-circle"
          class="absolute top-1 right-1 w-4 h-4"
        />
        <span
          v-else-if="gameStore.state.items[itemId]?.attempts > 0"
          class="absolute top-1 right-1 w-4 h-4 bg-yellow-500 text-white text-xs rounded-full flex items-center justify-center"
        >
          {{ gameStore.state.items[itemId].attempts }}
        </span>
      </div>
    </UButton>
  </div>
</template>

<script setup lang="ts">
interface Props {
  // No props needed as we're using the global store
}
const gameStore = useGameStore()

const handleItemClick = async (itemId: number) => {
  if (gameStore.state.items[itemId]?.completed) {
    return // Don't allow clicking completed items
  }

  // Check if item can be selected and navigate to problem page
  const canSelect = gameStore.selectItem(itemId)
  if (canSelect && gameStore.state.currentMode) {
    await navigateTo(`/problem/${gameStore.state.currentMode}/${itemId}`)
  }
}

const getItemVariant = (itemId: number) => {
  const item = gameStore.state.items[itemId]
  if (item?.completed) return 'solid'
  if (item?.attempts && item.attempts > 0) return 'soft'
  return 'outline'
}

const getItemColor = (itemId: number) => {
  const item = gameStore.state.items[itemId]
  if (item?.completed) return 'success'
  if (item?.attempts && item.attempts > 0) return 'warning'
  return 'primary'
}

const getItemClasses = (itemId: number) => {
  const item = gameStore.state.items[itemId]

  return {
    'completed': item?.completed,
    'attempted': (item?.attempts ?? 0) > 0 && !item?.completed,
    'available': !item?.completed
  }
}
</script>

